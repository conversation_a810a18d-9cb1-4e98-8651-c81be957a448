// {{CHENGQI:
// Action: Created; Timestamp: 2025-01-16 06:45:00; Reason: Shrimp Task ID: #57dc0ecc-773f-49c6-ab88-3079fa75e2b4, 创建Jest配置文件;
// }}
// {{START MODIFICATIONS}}

export default {
  // 测试环境
  testEnvironment: 'node',

  // 支持ES模块
  preset: null,
  
  // 测试文件匹配模式
  testMatch: [
    '<rootDir>/tests/**/*.test.js',
    '<rootDir>/tests/**/*.spec.js'
  ],
  
  // 忽略的文件和目录
  testPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/dist/',
    '<rootDir>/build/'
  ],
  
  // 覆盖率配置
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html', 'json'],
  
  // 覆盖率收集的文件
  collectCoverageFrom: [
    'model/**/*.js',
    'components/**/*.js',
    'apps/**/*.js',
    '!**/node_modules/**',
    '!**/tests/**',
    '!**/coverage/**',
    '!jest.config.js'
  ],
  
  // 覆盖率阈值
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 85,
      lines: 90,
      statements: 90
    }
  },
  
  // 设置文件
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  
  // 测试超时
  testTimeout: 10000,
  
  // 详细输出
  verbose: true,
  
  // 清除模拟
  clearMocks: true,
  restoreMocks: true,
  
  // 转换配置
  transform: {
    '^.+\\.js$': ['babel-jest', {
      presets: [
        ['@babel/preset-env', {
          targets: { node: 'current' },
          modules: 'auto'
        }]
      ]
    }]
  },

  // 模块文件扩展名
  moduleFileExtensions: ['js', 'json']
};

// {{END MODIFICATIONS}}
