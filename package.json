{"name": "werewolf-plugin", "version": "0.6.0", "description": "狼人杀游戏插件 for Miao-Yunzai", "type": "module", "main": "index.js", "scripts": {"test": "jest", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:verbose": "jest --verbose", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "keywords": ["werewolf", "game", "yunzai", "plugin"], "author": "Your Name", "license": "MIT", "devDependencies": {"@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "babel-jest": "^29.7.0", "eslint": "^8.50.0", "jest": "^29.7.0"}, "dependencies": {"lodash": "^4.17.21", "yaml": "^2.3.0", "chokidar": "^3.5.0"}}