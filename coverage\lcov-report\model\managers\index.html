
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for model/managers</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> model/managers</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">87.64% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>149/170</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">78.2% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>61/78</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">91.17% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>31/34</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">88.95% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>145/163</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="PlayerManager.js"><a href="PlayerManager.js.html">PlayerManager.js</a></td>
	<td data-value="87.95" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 87%"></div><div class="cover-empty" style="width: 13%"></div></div>
	</td>
	<td data-value="87.95" class="pct medium">87.95%</td>
	<td data-value="83" class="abs medium">73/83</td>
	<td data-value="78.04" class="pct medium">78.04%</td>
	<td data-value="41" class="abs medium">32/41</td>
	<td data-value="85" class="pct high">85%</td>
	<td data-value="20" class="abs high">17/20</td>
	<td data-value="87.34" class="pct medium">87.34%</td>
	<td data-value="79" class="abs medium">69/79</td>
	</tr>

<tr>
	<td class="file medium" data-value="StateManager.js"><a href="StateManager.js.html">StateManager.js</a></td>
	<td data-value="87.35" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 87%"></div><div class="cover-empty" style="width: 13%"></div></div>
	</td>
	<td data-value="87.35" class="pct medium">87.35%</td>
	<td data-value="87" class="abs medium">76/87</td>
	<td data-value="78.37" class="pct medium">78.37%</td>
	<td data-value="37" class="abs medium">29/37</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="14" class="abs high">14/14</td>
	<td data-value="90.47" class="pct high">90.47%</td>
	<td data-value="84" class="abs high">76/84</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-16T03:31:24.722Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    