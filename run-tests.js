// {{CHENGQI:
// Action: Created; Timestamp: 2025-01-16 07:20:00; Reason: Shrimp Task ID: #57dc0ecc-773f-49c6-ab88-3079fa75e2b4, 创建测试运行脚本;
// }}
// {{START MODIFICATIONS}}

/**
 * 测试运行脚本
 * 用于在没有npm的环境中运行测试
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 测试配置
const testConfigs = {
  unit: {
    name: '单元测试',
    pattern: 'tests/unit/**/*.test.js',
    description: '测试单个组件的功能'
  },
  integration: {
    name: '集成测试', 
    pattern: 'tests/integration/**/*.test.js',
    description: '测试组件间的协作'
  },
  all: {
    name: '全部测试',
    pattern: 'tests/**/*.test.js',
    description: '运行所有测试'
  }
};

/**
 * 运行测试
 * @param {string} type 测试类型
 * @param {Object} options 选项
 */
async function runTests(type = 'all', options = {}) {
  const config = testConfigs[type];
  if (!config) {
    console.error(`未知的测试类型: ${type}`);
    console.log('可用类型:', Object.keys(testConfigs).join(', '));
    process.exit(1);
  }

  console.log(`\n🧪 开始运行${config.name}`);
  console.log(`📝 ${config.description}`);
  console.log(`📂 测试模式: ${config.pattern}\n`);

  // 构建Jest命令
  const jestArgs = [
    '--config', 'jest.config.js',
    '--testPathPattern', config.pattern
  ];

  // 添加选项
  if (options.coverage) {
    jestArgs.push('--coverage');
  }
  if (options.verbose) {
    jestArgs.push('--verbose');
  }
  if (options.watch) {
    jestArgs.push('--watch');
  }

  return new Promise((resolve, reject) => {
    const jest = spawn('node', [
      join(__dirname, 'node_modules', '.bin', 'jest'),
      ...jestArgs
    ], {
      stdio: 'inherit',
      cwd: __dirname
    });

    jest.on('close', (code) => {
      if (code === 0) {
        console.log(`\n✅ ${config.name}完成`);
        resolve(code);
      } else {
        console.log(`\n❌ ${config.name}失败 (退出码: ${code})`);
        reject(new Error(`测试失败，退出码: ${code}`));
      }
    });

    jest.on('error', (error) => {
      console.error(`\n💥 运行测试时出错:`, error);
      reject(error);
    });
  });
}

/**
 * 检查测试环境
 */
function checkEnvironment() {
  console.log('🔍 检查测试环境...');
  
  // 检查Node.js版本
  const nodeVersion = process.version;
  console.log(`Node.js版本: ${nodeVersion}`);
  
  if (parseInt(nodeVersion.slice(1)) < 16) {
    console.warn('⚠️  建议使用Node.js 16或更高版本');
  }

  // 检查必要的文件
  const requiredFiles = [
    'jest.config.js',
    'tests/setup.js'
  ];

  for (const file of requiredFiles) {
    try {
      const fs = await import('fs');
      fs.accessSync(join(__dirname, file));
      console.log(`✅ ${file} 存在`);
    } catch (error) {
      console.error(`❌ ${file} 不存在`);
      process.exit(1);
    }
  }

  console.log('✅ 环境检查完成\n');
}

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log(`
🧪 狼人杀插件测试运行器

用法: node run-tests.js [类型] [选项]

测试类型:
  unit        运行单元测试
  integration 运行集成测试  
  all         运行所有测试 (默认)

选项:
  --coverage  生成覆盖率报告
  --verbose   详细输出
  --watch     监视模式
  --help      显示此帮助信息

示例:
  node run-tests.js unit --coverage
  node run-tests.js integration --verbose
  node run-tests.js all --watch
`);
}

/**
 * 解析命令行参数
 */
function parseArgs() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    process.exit(0);
  }

  const type = args.find(arg => !arg.startsWith('--')) || 'all';
  const options = {
    coverage: args.includes('--coverage'),
    verbose: args.includes('--verbose'),
    watch: args.includes('--watch')
  };

  return { type, options };
}

/**
 * 主函数
 */
async function main() {
  try {
    console.log('🎯 狼人杀插件测试套件');
    console.log('=' .repeat(50));

    await checkEnvironment();
    
    const { type, options } = parseArgs();
    
    await runTests(type, options);
    
    console.log('\n🎉 测试运行完成!');
    
  } catch (error) {
    console.error('\n💥 测试运行失败:', error.message);
    process.exit(1);
  }
}

// 运行主函数
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { runTests, checkEnvironment };

// {{END MODIFICATIONS}}
